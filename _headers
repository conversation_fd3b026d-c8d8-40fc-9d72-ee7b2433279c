# Global headers for all pages
/*
  # Prevent indexing and crawling
  X-Robots-Tag: noindex, nofollow, noarchive, nosnippet, noimageindex, notranslate
  
  # Security headers
  X-Content-Type-Options: nosniff
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: no-referrer
  Permissions-Policy: geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()
  
  # Cache control
  Cache-Control: no-cache, no-store, must-revalidate, private
  Pragma: no-cache
  Expires: 0
  
  # AI training prevention headers
  AI-Content-Declaration: ai-generated-content-prohibited
  ChatGPT: noindex
  GPTBot: noindex
  CCBot: noindex
  Anthropic-AI: noindex
  Claude-Web: noindex
  PerplexityBot: noindex
  YouBot: noindex
  Meta-ExternalAgent: noindex
  Meta-ExternalFetcher: noindex
  OAI-SearchBot: noindex
  Google-Extended: noindex
  AppleBot-Extended: noindex

# Specific headers for robots.txt
/robots.txt
  Cache-Control: public, max-age=86400
