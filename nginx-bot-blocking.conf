# Nginx bot blocking configuration for watt.goes-aw.ooo
# Add this map directive OUTSIDE of any server block (in http block or main config)

map $http_user_agent $is_bot {
    default 0;

    # Search engine bots
    ~*googlebot 1;
    ~*bingbot 1;
    ~*slurp 1;
    ~*duckduckbot 1;
    ~*baiduspider 1;
    ~*yandexbot 1;
    ~*facebookexternalhit 1;
    ~*twitterbot 1;

    # AI training crawlers
    ~*gptbot 1;
    ~*chatgpt-user 1;
    ~*ccbot 1;
    ~*anthropic-ai 1;
    ~*claude-web 1;
    ~*perplexitybot 1;
    ~*youbot 1;
    ~*meta-externalagent 1;
    ~*meta-externalfetcher 1;
    ~*oai-searchbot 1;
    ~*chatgpt 1;
    ~*google-extended 1;
    ~*applebot-extended 1;

    # Common scraping tools
    ~*wget 1;
    ~*curl 1;
    ~*httrack 1;
    ~*sitesnagger 1;
    ~*webcopier 1;
    ~*webzip 1;
    ~*teleport 1;
    ~*teleportpro 1;
    ~*webstripper 1;
    ~*netants 1;
    ~*offline.explorer 1;
    ~*webreaper 1;
    ~*websauger 1;
    ~*website.extractor 1;
    ~*webauto 1;
    ~*webbandit 1;
    ~*webwhacker 1;
    ~*python 1;
    ~*requests 1;
    ~*scrapy 1;
    ~*beautifulsoup 1;
    ~*selenium 1;
    ~*phantomjs 1;
    ~*headless 1;
    ~*puppeteer 1;
    ~*playwright 1;
    ~*bot 1;
    ~*crawl 1;
    ~*spider 1;
    ~*scrape 1;
}

# Configuration snippets to add to your existing server block for watt.goes-aw.ooo

# ADD THIS TO THE TOP OF YOUR EXISTING SERVER BLOCK:
# Block bots globally for this server
if ($is_bot) {
    return 403;
}

    location = /ref-sheet {
        # Block bots for this specific location
        if ($is_bot) {
            return 403;
        }

        # Add anti-indexing and security headers
        add_header X-Robots-Tag "noindex, nofollow, noarchive, nosnippet, noimageindex, notranslate" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-Frame-Options "DENY" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "no-referrer" always;
        add_header Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()" always;

        # Override cache control for bot protection (instead of 12h)
        add_header Cache-Control "no-cache, no-store, must-revalidate, private" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;

        # AI training prevention headers
        add_header AI-Content-Declaration "ai-generated-content-prohibited" always;
        add_header ChatGPT "noindex" always;
        add_header GPTBot "noindex" always;
        add_header CCBot "noindex" always;
        add_header Anthropic-AI "noindex" always;
        add_header Claude-Web "noindex" always;
        add_header PerplexityBot "noindex" always;
        add_header YouBot "noindex" always;
        add_header Meta-ExternalAgent "noindex" always;
        add_header Meta-ExternalFetcher "noindex" always;
        add_header OAI-SearchBot "noindex" always;
        add_header Google-Extended "noindex" always;
        add_header AppleBot-Extended "noindex" always;

        # Your existing configuration
        alias /var/www/watt.goes-aw.ooo/ref-sheet.html;
        default_type text/html;
        access_log off;
        # Note: expires 12h removed due to no-cache headers above
    }

    location = /ref {
        # Block bots for this specific location
        if ($is_bot) {
            return 403;
        }

        # Add anti-indexing and security headers
        add_header X-Robots-Tag "noindex, nofollow, noarchive, nosnippet, noimageindex, notranslate" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-Frame-Options "DENY" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "no-referrer" always;
        add_header Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()" always;

        # Override cache control for bot protection (instead of 12h)
        add_header Cache-Control "no-cache, no-store, must-revalidate, private" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;

        # AI training prevention headers
        add_header AI-Content-Declaration "ai-generated-content-prohibited" always;
        add_header ChatGPT "noindex" always;
        add_header GPTBot "noindex" always;
        add_header CCBot "noindex" always;
        add_header Anthropic-AI "noindex" always;
        add_header Claude-Web "noindex" always;
        add_header PerplexityBot "noindex" always;
        add_header YouBot "noindex" always;
        add_header Meta-ExternalAgent "noindex" always;
        add_header Meta-ExternalFetcher "noindex" always;
        add_header OAI-SearchBot "noindex" always;
        add_header Google-Extended "noindex" always;
        add_header AppleBot-Extended "noindex" always;

        # Your existing configuration
        alias /var/www/watt.goes-aw.ooo/ref-sheet.html;
        default_type text/html;
        access_log off;
        # Note: expires 12h removed due to no-cache headers above
    }

    # Serve the reference sheet assets with bot protection
    location /a/ {
        # Block bots from accessing images too
        if ($is_bot) {
            return 403;
        }

        # Prevent hotlinking of images
        valid_referers none blocked server_names
                       *.watt.goes-aw.ooo watt.goes-aw.ooo
                       *.localhost localhost;
        if ($invalid_referer) {
            return 403;
        }

        # Your existing configuration
        alias /var/www/watt.goes-aw.ooo/;
        access_log off;
        expires 30d;
    }

    # Allow robots.txt to be accessed (so bots can read the disallow rules)
    location = /robots.txt {
        add_header Cache-Control "public, max-age=86400";
        alias /var/www/watt.goes-aw.ooo/robots.txt;
    }

    # Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Your existing redirect for everything else
    location / {
        return 301 https://bsky.app/profile/watt.goes-aw.ooo;
    }

    # Your existing SSL configuration
    listen [::]:443 ssl; # managed by Certbot
    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/watt.goes-aw.ooo/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/watt.goes-aw.ooo/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}

# Your existing HTTP redirect server block
server {
    if ($host = watt.goes-aw.ooo) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    listen 80;
    listen [::]:80;
    server_name watt.goes-aw.ooo;
    return 404; # managed by Certbot
}


# INSTALLATION INSTRUCTIONS:
#
# 1. Add the map directive (lines 3-58) to your main nginx.conf file
#    in the http block, OUTSIDE of any server block
#
# 2. Replace your existing server block with the server block above (lines 67-215)
#    OR manually add the bot blocking and headers to your existing locations
#
# 3. Test configuration: sudo nginx -t
# 4. Reload nginx: sudo nginx -s reload
#
# ALTERNATIVE MINIMAL APPROACH:
# If you prefer to keep your existing config and just add minimal bot blocking,
# add these lines to each of your location blocks:
#
#     if ($is_bot) {
#         return 403;
#     }
#     add_header X-Robots-Tag "noindex, nofollow, noarchive, nosnippet, noimageindex, notranslate" always;
#     add_header Cache-Control "no-cache, no-store, must-revalidate, private" always;