# Bot detection map for nginx
# Include this file in the http block of your main nginx.conf:
# include /etc/nginx/bot-detection-map.conf;

map $http_user_agent $is_bot {
    default 0;
    
    # Search engine bots
    ~*googlebot 1;
    ~*bingbot 1;
    ~*slurp 1;
    ~*duckduckbot 1;
    ~*baiduspider 1;
    ~*yandexbot 1;
    ~*facebookexternalhit 1;
    ~*twitterbot 1;
    
    # AI training crawlers
    ~*gptbot 1;
    ~*chatgpt-user 1;
    ~*ccbot 1;
    ~*anthropic-ai 1;
    ~*claude-web 1;
    ~*perplexitybot 1;
    ~*youbot 1;
    ~*meta-externalagent 1;
    ~*meta-externalfetcher 1;
    ~*oai-searchbot 1;
    ~*chatgpt 1;
    ~*google-extended 1;
    ~*applebot-extended 1;
    
    # Common scraping tools
    ~*wget 1;
    ~*curl 1;
    ~*httrack 1;
    ~*sitesnagger 1;
    ~*webcopier 1;
    ~*webzip 1;
    ~*teleport 1;
    ~*teleportpro 1;
    ~*webstripper 1;
    ~*netants 1;
    ~*offline.explorer 1;
    ~*webreaper 1;
    ~*websauger 1;
    ~*website.extractor 1;
    ~*webauto 1;
    ~*webbandit 1;
    ~*webwhacker 1;
    ~*python 1;
    ~*requests 1;
    ~*scrapy 1;
    ~*beautifulsoup 1;
    ~*selenium 1;
    ~*phantomjs 1;
    ~*headless 1;
    ~*puppeteer 1;
    ~*playwright 1;
    ~*bot 1;
    ~*crawl 1;
    ~*spider 1;
    ~*scrape 1;
}
