# Block search engine and AI crawlers
<RequireAll>
    Require all granted
    
    # Block common search engine bots
    Require not env bad_bot
    
    # Set environment variable for known bots
    SetEnvIfNoCase User-Agent "googlebot" bad_bot
    SetEnvIfNoCase User-Agent "bingbot" bad_bot
    SetEnvIfNoCase User-Agent "slurp" bad_bot
    SetEnvIfNoCase User-Agent "duckduckbot" bad_bot
    SetEnvIfNoCase User-Agent "baiduspider" bad_bot
    SetEnvIfNoCase User-Agent "yandexbot" bad_bot
    SetEnvIfNoCase User-Agent "facebookexternalhit" bad_bot
    SetEnvIfNoCase User-Agent "twitterbot" bad_bot
    
    # Block AI training crawlers
    SetEnvIfNoCase User-Agent "gptbot" bad_bot
    SetEnvIfNoCase User-Agent "chatgpt-user" bad_bot
    SetEnvIfNoCase User-Agent "ccbot" bad_bot
    SetEnvIfNoCase User-Agent "anthropic-ai" bad_bot
    SetEnvIfNoCase User-Agent "claude-web" bad_bot
    SetEnvIfNoCase User-Agent "perplexitybot" bad_bot
    SetEnvIfNoCase User-Agent "youbot" bad_bot
    SetEnvIfNoCase User-Agent "meta-externalagent" bad_bot
    SetEnvIfNoCase User-Agent "meta-externalfetcher" bad_bot
    SetEnvIfNoCase User-Agent "oai-searchbot" bad_bot
    SetEnvIfNoCase User-Agent "chatgpt" bad_bot
    SetEnvIfNoCase User-Agent "google-extended" bad_bot
    SetEnvIfNoCase User-Agent "applebot-extended" bad_bot
    
    # Block common scraping tools
    SetEnvIfNoCase User-Agent "wget" bad_bot
    SetEnvIfNoCase User-Agent "curl" bad_bot
    SetEnvIfNoCase User-Agent "httrack" bad_bot
    SetEnvIfNoCase User-Agent "sitesnagger" bad_bot
    SetEnvIfNoCase User-Agent "webcopier" bad_bot
    SetEnvIfNoCase User-Agent "webzip" bad_bot
    SetEnvIfNoCase User-Agent "teleport" bad_bot
    SetEnvIfNoCase User-Agent "teleportpro" bad_bot
    SetEnvIfNoCase User-Agent "webstripper" bad_bot
    SetEnvIfNoCase User-Agent "netants" bad_bot
    SetEnvIfNoCase User-Agent "offline explorer" bad_bot
    SetEnvIfNoCase User-Agent "webreaper" bad_bot
    SetEnvIfNoCase User-Agent "websauger" bad_bot
    SetEnvIfNoCase User-Agent "website extractor" bad_bot
    SetEnvIfNoCase User-Agent "webauto" bad_bot
    SetEnvIfNoCase User-Agent "webbandit" bad_bot
    SetEnvIfNoCase User-Agent "webwhacker" bad_bot
    SetEnvIfNoCase User-Agent "python" bad_bot
    SetEnvIfNoCase User-Agent "requests" bad_bot
    SetEnvIfNoCase User-Agent "scrapy" bad_bot
    SetEnvIfNoCase User-Agent "beautifulsoup" bad_bot
    SetEnvIfNoCase User-Agent "selenium" bad_bot
    SetEnvIfNoCase User-Agent "phantomjs" bad_bot
    SetEnvIfNoCase User-Agent "headless" bad_bot
    SetEnvIfNoCase User-Agent "puppeteer" bad_bot
    SetEnvIfNoCase User-Agent "playwright" bad_bot
</RequireAll>

# Add security headers
<IfModule mod_headers.c>
    # Prevent indexing
    Header always set X-Robots-Tag "noindex, nofollow, noarchive, nosnippet, noimageindex, notranslate"
    
    # Security headers
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "DENY"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "no-referrer"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()"
    
    # Cache control to prevent caching
    Header always set Cache-Control "no-cache, no-store, must-revalidate, private"
    Header always set Pragma "no-cache"
    Header always set Expires "0"
    
    # AI training prevention headers
    Header always set AI-Content-Declaration "ai-generated-content-prohibited"
    Header always set ChatGPT "noindex"
    Header always set GPTBot "noindex"
    Header always set CCBot "noindex"
    Header always set Anthropic-AI "noindex"
    Header always set Claude-Web "noindex"
    Header always set PerplexityBot "noindex"
    Header always set YouBot "noindex"
    Header always set Meta-ExternalAgent "noindex"
    Header always set Meta-ExternalFetcher "noindex"
    Header always set OAI-SearchBot "noindex"
    Header always set Google-Extended "noindex"
    Header always set AppleBot-Extended "noindex"
</IfModule>

# Disable server signature
ServerSignature Off

# Disable directory browsing
Options -Indexes

# Protect sensitive files
<Files "robots.txt">
    Order allow,deny
    Allow from all
</Files>

<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

# Rate limiting (if mod_evasive is available)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        5
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>

# Prevent hotlinking of images
RewriteEngine On
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?watt\.goes-aw\.ooo [NC]
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?localhost [NC]
RewriteRule \.(jpg|jpeg|png|gif|webp|svg)$ - [F,L]
